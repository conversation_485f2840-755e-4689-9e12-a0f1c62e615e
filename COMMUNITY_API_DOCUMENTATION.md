# Community API Documentation

## Overview
The Community API provides comprehensive CRUD operations for managing communities with membership functionality, post integration, and JWT authentication. Communities allow users to create spaces for shared interests where members can post and interact.

## Base URL
```
http://localhost:5001/api/communities
```

## Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

## Data Models

### Community Schema
```typescript
{
  title: String (required, max 100 chars),
  backgroundImage: String (S3 URL, default provided),
  description: String (required, max 500 chars),
  author: ObjectId (ref: User, community creator),
  members: [CommunityMember],
  membersCount: Number (auto-calculated),
  createdAt: Date,
  updatedAt: Date
}
```

### Community Member Schema
```typescript
{
  userId: ObjectId (ref: User),
  username: String,
  profilePhoto: String,
  joinedAt: Date
}
```

## Endpoints

### 1. Create Community
**POST** `/create`

Creates a new community with the authenticated user as the author.

**Content-Type:** `multipart/form-data`

**Body Parameters:**
- `title` (string, required): Community title (max 100 chars)
- `description` (string, required): Community description (max 500 chars)
- `backgroundImage` (file, optional): Background image (JPEG, PNG, JPG, WEBP, max 10MB)

**Response:**
```json
{
  "message": "Community created successfully",
  "community": {
    "_id": "community_id",
    "title": "Community Title",
    "description": "Community description",
    "backgroundImage": "s3_url",
    "author": {
      "_id": "user_id",
      "username": "author_username",
      "profilePhoto": "profile_url"
    },
    "members": [...],
    "membersCount": 1,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

### 2. Update Community
**PATCH** `/:communityId`

Updates community details. Only the community author can update.

**Content-Type:** `multipart/form-data`

**Body Parameters:**
- `title` (string, optional): New community title
- `description` (string, optional): New community description
- `backgroundImage` (file, optional): New background image

**Response:**
```json
{
  "message": "Community updated successfully",
  "community": { /* updated community object */ }
}
```

### 3. Delete Community
**DELETE** `/:communityId`

Deletes a community and all associated data. Only the community author can delete.

**Cascading Effects:**
- Deletes all posts in the community
- Removes all media files from S3
- Removes all members
- Deletes background image (if not default)

**Response:**
```json
{
  "message": "Community and all associated data deleted successfully"
}
```

### 4. Get All Communities
**GET** `/all`

Retrieves all communities with join status for the authenticated user.

**Response:**
```json
{
  "message": "Communities retrieved successfully",
  "communities": [
    {
      /* community object */,
      "isJoined": true/false
    }
  ]
}
```

### 5. Join Community
**POST** `/:communityId/join`

Joins a community. Users cannot join if already a member.

**Response:**
```json
{
  "message": "Successfully joined the community",
  "community": {
    "_id": "community_id",
    "title": "Community Title",
    "membersCount": 5,
    "isJoined": true
  }
}
```

### 6. Leave Community
**POST** `/:communityId/leave`

Leaves a community. Community authors cannot leave their own community.

**Note:** When a member leaves, their posts remain in the community.

**Response:**
```json
{
  "message": "Successfully left the community",
  "community": {
    "_id": "community_id",
    "title": "Community Title",
    "membersCount": 4,
    "isJoined": false
  }
}
```

### 7. Get User Created Communities
**GET** `/my-created`

Retrieves all communities created by the authenticated user.

**Response:**
```json
{
  "message": "User created communities retrieved successfully",
  "communities": [ /* array of communities */ ]
}
```

### 8. Get User Joined Communities
**GET** `/my-joined`

Retrieves all communities the authenticated user has joined.

**Response:**
```json
{
  "message": "User joined communities retrieved successfully",
  "communities": [
    {
      /* community object */,
      "isJoined": true
    }
  ]
}
```

### 9. Get Top 10 Communities
**GET** `/top`

Retrieves the top 10 communities by member count.

**Response:**
```json
{
  "message": "Top 10 communities retrieved successfully",
  "communities": [
    {
      /* community object */,
      "isJoined": true/false
    }
  ]
}
```

### 10. Get Community Members
**GET** `/:communityId/members`

Retrieves member count and member list for a community.

**Response:**
```json
{
  "message": "Community member count retrieved successfully",
  "communityId": "community_id",
  "title": "Community Title",
  "membersCount": 10,
  "members": [
    {
      "userId": "user_id",
      "username": "username",
      "profilePhoto": "profile_url",
      "joinedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

### 11. Get Community Posts
**GET** `/:communityId/posts`

Retrieves all posts in a community, sorted by creation date (newest first).

**Response:**
```json
{
  "message": "Community posts retrieved successfully",
  "community": {
    "_id": "community_id",
    "title": "Community Title",
    "membersCount": 10
  },
  "posts": [
    {
      /* post object with populated user and community info */
    }
  ]
}
```

## Post Integration

### Creating Community Posts
When creating posts via the existing `/api/posts/create` endpoint, include `communityId` in the request body:

```json
{
  "description": "Post description",
  "communityId": "community_id"
}
```

**Requirements:**
- User must be a member of the community to post
- Community must exist
- All existing post features (media upload, likes, comments) work with community posts

## Features

### Membership Management
- Automatic member tracking with join/leave functionality
- Real-time member count updates
- Author cannot leave their own community (must delete instead)
- Member posts remain when they leave the community

### Media Upload
- Background image upload with validation (JPEG, PNG, JPG, WEBP)
- Maximum 10MB file size for background images
- Automatic S3 upload and cleanup
- Default background image provided

### Data Integrity
- Cascading deletion when community is deleted
- Automatic cleanup of media files
- Member count synchronization
- Post-community relationship tracking

### Security
- JWT authentication required for all endpoints
- Author-only permissions for update/delete operations
- Member-only posting permissions
- File upload validation and size limits

## Error Responses

### Common Error Codes
- `400` - Bad Request (validation errors, already member, etc.)
- `401` - Unauthorized (missing/invalid token)
- `403` - Forbidden (not author, not member)
- `404` - Not Found (community not found, user not found)
- `500` - Internal Server Error

### Example Error Response
```json
{
  "message": "You must be a member of the community to post"
}
```
