import { Router } from 'express';
import {
  createPost,
  getUserPosts,
  getPostById,
  updatePost,
  deletePost,
  togglePostLike,
  addComment,
  deleteComment,
  toggleCommentLike,
  getPostLikesCount,
  getPostCommentsCount,
  getCommentLikesCount
} from '../controllers/post.controller';
import { authenticateToken } from '../middlewares/index';
import { validatePostMedia } from '../utils/validatePostMedia';

const router = Router();

// Apply JWT authentication to all routes
router.use(authenticateToken);

// Post CRUD operations
router.post('/create', validatePostMedia, createPost);
router.get('/my-posts', getUserPosts);
router.get('/:postId', getPostById);
router.patch('/:postId', updatePost);
router.delete('/:postId', deletePost);

// Post interaction routes
router.post('/:postId/like', togglePostLike);
router.post('/:postId/comment', addComment);
router.post('/:postId/comment/:commentId/like', toggleCommentLike);
router.delete('/:postId/comments/:commentId', deleteComment);
// Count routes
router.get('/:postId/likes/count', getPostLikesCount);
router.get('/:postId/comments/count', getPostCommentsCount);
router.get('/:postId/comment/:commentId/likes/count', getCommentLikesCount);

export default router;
