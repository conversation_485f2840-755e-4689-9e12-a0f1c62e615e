# Community Module Testing Guide

## Prerequisites
- Server running on `http://localhost:5001`
- Valid JWT token for authentication
- Test images for background upload
- API testing tool (Postman, Thunder Client, etc.)

## Authentication Setup
All requests require JWT authentication:
```
Authorization: Bearer <your_jwt_token>
```

## Test Scenarios

### 1. Community Creation Tests

#### Test 1.1: Create Community with Background Image
```
POST /api/communities/create
Content-Type: multipart/form-data

Body:
- title: "Tech Enthusiasts"
- description: "A community for technology lovers"
- backgroundImage: [upload image file]
```

**Expected Result:** Community created successfully with uploaded background image

#### Test 1.2: Create Community without Background Image
```
POST /api/communities/create
Content-Type: multipart/form-data

Body:
- title: "Book Club"
- description: "Share and discuss your favorite books"
```

**Expected Result:** Community created with default background image

#### Test 1.3: Create Community with Invalid Data
```
POST /api/communities/create
Content-Type: multipart/form-data

Body:
- title: ""
- description: "Invalid title test"
```

**Expected Result:** 400 error - "Community title is required"

### 2. Community Update Tests

#### Test 2.1: Update Community as Author
```
PATCH /api/communities/{communityId}
Content-Type: multipart/form-data

Body:
- title: "Updated Tech Community"
- description: "Updated description"
- backgroundImage: [new image file]
```

**Expected Result:** Community updated successfully

#### Test 2.2: Update Community as Non-Author
Use a different user's JWT token to attempt update.

**Expected Result:** 403 error - "Only the community author can update the community"

### 3. Community Membership Tests

#### Test 3.1: Join Community
```
POST /api/communities/{communityId}/join
```

**Expected Result:** Successfully joined, member count increased

#### Test 3.2: Join Already Joined Community
```
POST /api/communities/{communityId}/join
```

**Expected Result:** 400 error - "You are already a member of this community"

#### Test 3.3: Leave Community
```
POST /api/communities/{communityId}/leave
```

**Expected Result:** Successfully left, member count decreased

#### Test 3.4: Author Tries to Leave Own Community
```
POST /api/communities/{communityId}/leave
```

**Expected Result:** 400 error - "Community author cannot leave their own community"

### 4. Community Query Tests

#### Test 4.1: Get All Communities
```
GET /api/communities/all
```

**Expected Result:** List of all communities with isJoined status

#### Test 4.2: Get User Created Communities
```
GET /api/communities/my-created
```

**Expected Result:** Communities created by the authenticated user

#### Test 4.3: Get User Joined Communities
```
GET /api/communities/my-joined
```

**Expected Result:** Communities the user has joined

#### Test 4.4: Get Top 10 Communities
```
GET /api/communities/top
```

**Expected Result:** Top 10 communities by member count

#### Test 4.5: Get Community Members
```
GET /api/communities/{communityId}/members
```

**Expected Result:** Member count and member list

#### Test 4.6: Get Community Posts
```
GET /api/communities/{communityId}/posts
```

**Expected Result:** All posts in the community

### 5. Community Post Integration Tests

#### Test 5.1: Create Community Post as Member
```
POST /api/posts/create
Content-Type: multipart/form-data

Body:
- description: "This is a community post"
- communityId: "{communityId}"
- media: [optional media files]
```

**Expected Result:** Post created successfully in the community

#### Test 5.2: Create Community Post as Non-Member
```
POST /api/posts/create
Content-Type: multipart/form-data

Body:
- description: "This should fail"
- communityId: "{communityId}"
```

**Expected Result:** 403 error - "You must be a member of the community to post"

#### Test 5.3: Create Regular Post (No Community)
```
POST /api/posts/create
Content-Type: multipart/form-data

Body:
- description: "Regular post without community"
```

**Expected Result:** Post created successfully without community association

### 6. Community Deletion Tests

#### Test 6.1: Delete Community as Author
```
DELETE /api/communities/{communityId}
```

**Expected Result:** Community and all associated data deleted

#### Test 6.2: Delete Community as Non-Author
```
DELETE /api/communities/{communityId}
```

**Expected Result:** 403 error - "Only the community author can delete the community"

#### Test 6.3: Verify Cascading Deletion
After deleting a community:
1. Check that all community posts are deleted
2. Verify media files are removed from S3
3. Confirm background image is deleted (if not default)

### 7. Edge Case Tests

#### Test 7.1: Invalid Community ID
```
GET /api/communities/invalid-id/posts
```

**Expected Result:** 404 error - "Community not found"

#### Test 7.2: Large File Upload
Upload a background image larger than 10MB.

**Expected Result:** 400 error - "Background image size too large"

#### Test 7.3: Invalid File Type
Upload a non-image file as background.

**Expected Result:** 400 error - "Only image files are allowed"

#### Test 7.4: Member Leaves and Posts Remain
1. User joins community and creates posts
2. User leaves community
3. Check that posts remain in community

**Expected Result:** Posts remain after member leaves

## Testing Checklist

### Basic Functionality
- [ ] Create community with background image
- [ ] Create community without background image
- [ ] Update community (author only)
- [ ] Delete community (author only)
- [ ] Join community
- [ ] Leave community
- [ ] Get all communities
- [ ] Get user created communities
- [ ] Get user joined communities
- [ ] Get top communities
- [ ] Get community members
- [ ] Get community posts

### Post Integration
- [ ] Create community post as member
- [ ] Fail to create community post as non-member
- [ ] Create regular post without community
- [ ] Verify posts appear in community feed

### Security & Permissions
- [ ] All endpoints require authentication
- [ ] Only authors can update/delete communities
- [ ] Only members can post in communities
- [ ] Authors cannot leave their own communities

### Data Integrity
- [ ] Member count updates correctly
- [ ] Cascading deletion works properly
- [ ] Media files are cleaned up
- [ ] Posts remain when members leave

### Error Handling
- [ ] Invalid community ID handling
- [ ] File upload validation
- [ ] Duplicate join attempts
- [ ] Permission violations

## Performance Considerations

### Database Queries
- Communities are indexed by author and member count
- Member lookup is optimized with userId indexing
- Posts are sorted by creation date for community feeds

### File Management
- Background images are uploaded to S3
- Automatic cleanup on community deletion
- File size and type validation

### Memory Usage
- Member arrays are embedded in community documents
- Consider pagination for large member lists in future updates

## Common Issues

### File Upload Problems
- Ensure Content-Type is `multipart/form-data`
- Check file size limits (10MB for background images)
- Verify file type restrictions (images only)

### Authentication Issues
- Verify JWT token is valid and not expired
- Check Authorization header format
- Ensure user exists in database

### Permission Errors
- Confirm user is community author for update/delete operations
- Verify user is community member for posting
- Check community existence before operations
