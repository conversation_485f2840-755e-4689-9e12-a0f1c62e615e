import { Response } from 'express';
import Post from '../models/Post';
import Community from '../models/Community';
import { StatusCodes } from 'http-status-codes';
import { AuthenticatedRequest } from '../middlewares/auth';
import { uploadFile, deleteFile } from '../utils/mediaHandling';

// Create a new post
export const createPost = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { description, communityId } = req.body;  // we can use communityId from req.params as well
    const userId = req.user?.userId;
    const files = req.files as Express.Multer.File[];

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Validate required fields
    if (!description || description.trim() === '') {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Description is required'
      });
      return;
    }

    // If communityId is provided, validate community and membership
    if (communityId) {
      const community = await Community.findById(communityId);
      if (!community) {
        res.status(StatusCodes.NOT_FOUND).json({
          message: 'Community not found'
        });
        return;
      }

      // Check if user is a member of the community
      const isMember = community.members.some(member => member.userId.toString() === userId);
      if (!isMember) {
        res.status(StatusCodes.FORBIDDEN).json({
          message: 'You must be a member of the community to post'
        });
        return;
      }
    }

    let mediaUrls: string[] = [];

    // Upload media files to S3 if provided
    if (files && files.length > 0) {
      try {
        const uploadPromises = files.map(file => uploadFile(file));
        mediaUrls = await Promise.all(uploadPromises);
      } catch (uploadError) {
        console.error('Error uploading media files:', uploadError);
        res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
          message: 'Failed to upload media files'
        });
        return;
      }
    }

    const newPost = new Post({
      userId,
      communityId: communityId || undefined,
      description: description.trim(),
      media: mediaUrls,
      likes: [],
      comments: []
    });

    const savedPost = await newPost.save();
    await savedPost.populate('userId', 'username profilePhoto');
    if (communityId) {
      await savedPost.populate('communityId', 'title');
    }

    res.status(StatusCodes.CREATED).json({
      message: 'Post created successfully',
      post: savedPost
    });
  } catch (error) {
    console.error('Error creating post:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Failed to create post'
    });
  }
};

// // Get all posts including community posts of a user
// export const getUserPosts = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
//   try {
//     const userId = req.user?.userId;

//     if (!userId) {
//       res.status(StatusCodes.UNAUTHORIZED).json({
//         message: 'User not authenticated'
//       });
//       return;
//     }

//     const posts = await Post.find({ userId })
//       .populate('userId', 'username profilePhoto')
//       .populate('comments.userId', 'username profilePhoto')
//       .sort({ createdAt: -1 });

//     res.status(StatusCodes.OK).json({
//       message: 'Posts retrieved successfully',
//       posts,
//       count: posts.length
//     });
//   } catch (error) {
//     console.error('Error retrieving posts:', error);
//     res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
//       message: 'Failed to retrieve posts'
//     });
//   }
// };


// Get all posts that are not in any community
export const getUserPosts = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Find posts for the authenticated user where communityId is not defined (i.e., not in a community)
    const posts = await Post.find({ 
      userId, 
      communityId: { $exists: false }  // Filter to exclude posts that belong to any community
    })
      .populate('userId', 'username profilePhoto')
      .populate('comments.userId', 'username profilePhoto')
      .sort({ createdAt: -1 });

    res.status(StatusCodes.OK).json({
      message: 'Posts retrieved successfully',
      posts,
      count: posts.length
    });
  } catch (error) {
    console.error('Error retrieving posts:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Failed to retrieve posts'
    });
  }
};


// Get a specific post by ID
export const getPostById = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { postId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const post = await Post.findOne({ _id: postId, userId })
      .populate('userId', 'username profilePhoto')
      .populate('comments.userId', 'username profilePhoto');

    if (!post) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Post not found'
      });
      return;
    }

    res.status(StatusCodes.OK).json({
      message: 'Post retrieved successfully',
      post
    });
  } catch (error) {
    console.error('Error retrieving post:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Failed to retrieve post'
    });
  }
};

// Update post description only
export const updatePost = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { postId } = req.params;
    const { description } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    if (!description || description.trim() === '') {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Description is required'
      });
      return;
    }

    const post = await Post.findOne({ _id: postId, userId });

    if (!post) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Post not found'
      });
      return;
    }

    post.description = description.trim();
    const updatedPost = await post.save();
    await updatedPost.populate('userId', 'username profilePhoto');

    res.status(StatusCodes.OK).json({
      message: 'Post updated successfully',
      post: updatedPost
    });
  } catch (error) {
    console.error('Error updating post:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Failed to update post'
    });
  }
};

// Delete a post and all its associated data
export const deletePost = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { postId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const post = await Post.findOne({ _id: postId, userId });

    if (!post) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Post not found'
      });
      return;
    }

    // Delete media files from S3 if they exist
    if (post.media && post.media.length > 0) {
      try {
        const deletePromises = post.media.map((mediaUrl: any)  => deleteFile(mediaUrl));
        await Promise.all(deletePromises);
      } catch (deleteError) {
        console.error('Error deleting media files:', deleteError);
        // Continue with post deletion even if media deletion fails
      }
    }

    await Post.findByIdAndDelete(postId);

    res.status(StatusCodes.OK).json({
      message: 'Post and all associated data deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting post:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Failed to delete post'
    });
  }
};

// Like/Unlike a post
export const togglePostLike = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { postId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const post = await Post.findById(postId);

    if (!post) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Post not found'
      });
      return;
    }

    const likeIndex = post.likes.indexOf(userId);
    let action: string;

    if (likeIndex > -1) {
      // User already liked, so unlike
      post.likes.splice(likeIndex, 1);
      action = 'unliked';
    } else {
      // User hasn't liked, so like
      post.likes.push(userId);
      action = 'liked';
    }

    await post.save();

    res.status(StatusCodes.OK).json({
      message: `Post ${action} successfully`,
      likesCount: post.likes.length,
      isLiked: action === 'liked'
    });
  } catch (error) {
    console.error('Error toggling post like:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Failed to toggle post like'
    });
  }
};

// Add a comment to a post
export const addComment = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { postId } = req.params;
    const { content } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    if (!content || content.trim() === '') {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Comment content is required'
      });
      return;
    }

    const post = await Post.findById(postId);

    if (!post) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Post not found'
      });
      return;
    }

    const newComment = {
      userId,
      content: content.trim(),
      likes: []
    };

    post.comments.push(newComment as any);
    await post.save();

    // Populate the newly added comment
    await post.populate('comments.userId', 'username profilePhoto');

    const addedComment = post.comments[post.comments.length - 1];

    res.status(StatusCodes.CREATED).json({
      message: 'Comment added successfully',
      comment: addedComment,
      commentsCount: post.comments.length
    });
  } catch (error) {
    console.error('Error adding comment:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Failed to add comment'
    });
  }
};

export const deleteComment = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { postId, commentId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const post = await Post.findById(postId);

    if (!post) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Post not found'
      });
      return;
    }

    const commentIndex = post.comments.findIndex((comment : any) => comment._id.toString() === commentId);

    if (commentIndex === -1) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Comment not found'
      });
      return;
    }

    const comment = post.comments[commentIndex];

    // Check if the logged-in user is the owner of the comment
    if (comment.userId.toString() !== userId.toString()) {
      res.status(StatusCodes.FORBIDDEN).json({
        message: 'You can only delete your own comment'
      });
      return;
    }

    // Remove the comment from the post
    post.comments.splice(commentIndex, 1);
    await post.save();

    res.status(StatusCodes.OK).json({
      message: 'Comment deleted successfully',
      commentsCount: post.comments.length
    });
  } catch (error) {
    console.error('Error deleting comment:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Failed to delete comment'
    });
  }
};

// Like/Unlike a comment
export const toggleCommentLike = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { postId, commentId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const post = await Post.findById(postId);

    if (!post) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Post not found'
      });
      return;
    }

    const comment = (post.comments as any).id(commentId);

    if (!comment) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Comment not found'
      });
      return;
    }

    const likeIndex = comment.likes.indexOf(userId);
    let action: string;

    if (likeIndex > -1) {
      // User already liked, so unlike
      comment.likes.splice(likeIndex, 1);
      action = 'unliked';
    } else {
      // User hasn't liked, so like
      comment.likes.push(userId);
      action = 'liked';
    }

    (comment as any).updatedAt = new Date();
    await post.save();

    res.status(StatusCodes.OK).json({
      message: `Comment ${action} successfully`,
      likesCount: comment.likes.length,
      isLiked: action === 'liked'
    });
  } catch (error) {
    console.error('Error toggling comment like:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Failed to toggle comment like'
    });
  }
};

// Get likes count for a specific post
export const getPostLikesCount = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { postId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const post = await Post.findById(postId).select('likes');

    if (!post) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Post not found'
      });
      return;
    }

    const isLiked = post.likes.includes(userId);

    res.status(StatusCodes.OK).json({
      message: 'Post likes count retrieved successfully',
      likesCount: post.likes.length,
      isLiked
    });
  } catch (error) {
    console.error('Error getting post likes count:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Failed to get post likes count'
    });
  }
};

// Get comments count for a specific post
export const getPostCommentsCount = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { postId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const post = await Post.findById(postId).select('comments');

    if (!post) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Post not found'
      });
      return;
    }

    res.status(StatusCodes.OK).json({
      message: 'Post comments count retrieved successfully',
      commentsCount: post.comments.length
    });
  } catch (error) {
    console.error('Error getting post comments count:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Failed to get post comments count'
    });
  }
};

// Get likes count for a specific comment
export const getCommentLikesCount = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { postId, commentId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const post = await Post.findById(postId).select('comments');

    if (!post) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Post not found'
      });
      return;
    }

    const comment = (post.comments as any).id(commentId);

    if (!comment) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Comment not found'
      });
      return;
    }

    const isLiked = comment.likes.includes(userId);

    res.status(StatusCodes.OK).json({
      message: 'Comment likes count retrieved successfully',
      likesCount: comment.likes.length,
      isLiked
    });
  } catch (error) {
    console.error('Error getting comment likes count:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Failed to get comment likes count'
    });
  }
};
