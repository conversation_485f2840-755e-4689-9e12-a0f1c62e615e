import { Request, Response, NextFunction } from "express";
import multer, { MulterError } from "multer";

const communityImageUpload = multer({ 
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit for community background images
  },
  fileFilter: (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/webp'];

    if (!allowedTypes.includes(file.mimetype)) {
      return cb(new Error('Only image files (JPEG, PNG, JPG, WEBP) are allowed for community background!'));
    }
    cb(null, true);
  }
}).single('backgroundImage');

export const validateCommunityImage = (
  req: Request, 
  res: Response, 
  next: NextFunction
) => {
  communityImageUpload(req, res, (error: any) => {
    if (error) {
      console.error("Community Image Upload Error:", error);
      
      if (error instanceof MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
          res.status(400).json({ message: 'Background image size too large. Maximum 10MB allowed.' });
        } else {
          res.status(400).json({ message: `Upload Error: ${error.message}` });
        }
      } else {
        res.status(400).json({ message: error.message });  
      }
      
      req.destroy();
      return;
    }
    next();
  });
};
