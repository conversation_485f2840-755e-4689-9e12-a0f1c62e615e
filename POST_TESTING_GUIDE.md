# Post API Testing Guide

## Prerequisites
1. Server running on `http://localhost:5001`
2. Valid JWT token from authentication
3. Testing tool like <PERSON>man, Thunder Client, or curl

## Testing Steps

### 1. Authentication
First, get a JWT token by logging in:
```bash
POST http://localhost:5001/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "your-password"
}
```

Copy the `accessToken` from the response for use in subsequent requests.

### 2. Create a Post (Text Only)
```bash
POST http://localhost:5001/api/posts/create
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: multipart/form-data

Form Data:
- description: "This is my first post!"
```

### 3. Create a Post with Media
```bash
POST http://localhost:5001/api/posts/create
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: multipart/form-data

Form Data:
- description: "Post with images and videos"
- media: [select multiple image/video files]
```

### 4. Get All User Posts
```bash
GET http://localhost:5001/api/posts/my-posts
Authorization: Bearer YOUR_JWT_TOKEN
```

### 5. Get Specific Post
```bash
GET http://localhost:5001/api/posts/{POST_ID}
Authorization: Bearer YOUR_JWT_TOKEN
```

### 6. Update Post Description
```bash
PATCH http://localhost:5001/api/posts/{POST_ID}
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "description": "Updated post description"
}
```

### 7. Like a Post
```bash
POST http://localhost:5001/api/posts/{POST_ID}/like
Authorization: Bearer YOUR_JWT_TOKEN
```

### 8. Add Comment to Post
```bash
POST http://localhost:5001/api/posts/{POST_ID}/comment
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "content": "Great post! Thanks for sharing."
}
```

### 9. Like a Comment
```bash
POST http://localhost:5001/api/posts/{POST_ID}/comment/{COMMENT_ID}/like
Authorization: Bearer YOUR_JWT_TOKEN
```

### 10. Get Post Likes Count
```bash
GET http://localhost:5001/api/posts/{POST_ID}/likes/count
Authorization: Bearer YOUR_JWT_TOKEN
```

### 11. Get Post Comments Count
```bash
GET http://localhost:5001/api/posts/{POST_ID}/comments/count
Authorization: Bearer YOUR_JWT_TOKEN
```

### 12. Get Comment Likes Count
```bash
GET http://localhost:5001/api/posts/{POST_ID}/comment/{COMMENT_ID}/likes/count
Authorization: Bearer YOUR_JWT_TOKEN
```

### 13. Delete Post
```bash
DELETE http://localhost:5001/api/posts/{POST_ID}
Authorization: Bearer YOUR_JWT_TOKEN
```

## Expected Behaviors

### Media Upload
- Supports images and videos
- Maximum 10 files per post
- Maximum 50MB per file
- Files uploaded to AWS S3
- URLs stored in database

### Like System
- Toggle functionality (like/unlike)
- Real-time count updates
- User can see if they liked the post/comment

### Comment System
- Nested in post document
- Supports likes on comments
- Automatic timestamp management

### Data Cleanup
- Deleting a post removes all likes, comments, and media files
- Cascading deletion ensures data integrity

### Security
- JWT authentication required
- Users can only access their own posts
- File upload validation

## Testing Checklist

- [ ] Create post without media
- [ ] Create post with single image
- [ ] Create post with multiple media files
- [ ] Try uploading non-image/video file (should fail)
- [ ] Try uploading file larger than 50MB (should fail)
- [ ] Get all user posts
- [ ] Get specific post by ID
- [ ] Update post description
- [ ] Like and unlike post
- [ ] Add comment to post
- [ ] Like and unlike comment
- [ ] Check all count endpoints
- [ ] Delete post and verify cleanup
- [ ] Try accessing another user's post (should fail)

## Common Issues

### File Upload Issues
- Ensure Content-Type is `multipart/form-data`
- Check file size and type restrictions
- Verify AWS S3 credentials are configured

### Authentication Issues
- Ensure JWT token is valid and not expired
- Include "Bearer " prefix in Authorization header
- Check token format and encoding

### Database Issues
- Verify MongoDB connection
- Check if user exists in database
- Ensure proper ObjectId format for IDs
