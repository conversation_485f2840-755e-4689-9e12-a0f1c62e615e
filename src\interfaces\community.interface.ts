import { Document } from 'mongoose';

export interface ICommunityMember {
  userId: string;
  username: string;
  profilePhoto: string;
  joinedAt: Date;
}

export interface ICommunity extends Document {
  title: string;
  backgroundImage: string;
  description: string;
  author: string; // userId of the creator
  members: ICommunityMember[];
  membersCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface ICommunityWithJoinStatus extends ICommunity {
  isJoined: boolean;
}
