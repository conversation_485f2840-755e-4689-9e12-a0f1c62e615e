import mongoose, { Schema } from 'mongoose';
import { ICommunity, ICommunityMember } from '../interfaces/index';

const communityMemberSchema: Schema = new Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  username: {
    type: String,
    required: true
  },
  profilePhoto: {
    type: String,
    required: true
  },
  joinedAt: {
    type: Date,
    default: Date.now
  }
});

const communitySchema: Schema = new Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  backgroundImage: {
    type: String,
    default: "https://images.unsplash.com/photo-1557804506-669a67965ba0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 500
  },
  author: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  members: [communityMemberSchema],
  membersCount: {
    type: Number,
    default: 0
  }
}, { timestamps: true });

// Index for better query performance
communitySchema.index({ author: 1 });
communitySchema.index({ membersCount: -1 });
communitySchema.index({ 'members.userId': 1 });

const Community = mongoose.model<ICommunity>('Community', communitySchema);

export default Community;
