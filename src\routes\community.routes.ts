import { Router } from 'express';
import {
  createCommunity,
  updateCommunity,
  deleteCommunity,
  getAllCommunities,
  joinCommunity,
  leaveCommunity,
  getUserCreatedCommunities,
  getUserJoinedCommunities,
  getTopCommunities,
  getCommunityMemberCount,
  getCommunityPosts
} from '../controllers/community.controller';
import { createPost, getPostById, updatePost, deletePost } from '../controllers/post.controller';
import { authenticateToken } from '../middlewares/index';
import { validateCommunityImage } from '../utils/validateCommunityImage';
import { validatePostMedia } from '../utils/validatePostMedia';

const router = Router();

// Apply JWT authentication to all routes
router.use(authenticateToken);

// Community CRUD operations
router.post('/create', validateCommunityImage, createCommunity);
router.patch('/:communityId', validateCommunityImage, updateCommunity);
router.delete('/:communityId', deleteCommunity);

// Community query operations
router.get('/all', getAllCommunities);    //This is admin operation
router.get('/my-created', getUserCreatedCommunities);
router.get('/my-joined', getUserJoinedCommunities);
router.get('/top', getTopCommunities);

// Community membership operations
router.post('/:communityId/join', joinCommunity);
router.post('/:communityId/leave', leaveCommunity);

// Community member and post operations
router.get('/:communityId/members', getCommunityMemberCount);
router.get('/:communityId/posts', getCommunityPosts);


// CRUD operation of posts in a Community (same as post routes)
router.post('/:communtiyId/posts/create',validatePostMedia, createPost);
router.get('/:communtiyId/posts/:postId', getPostById);
router.patch('/:communtiyId/posts/:postId', updatePost);
router.delete('/:communtiyId/posts/:postId', deletePost);


// for post like/unlike , comment, like/unlike comment , and get number of likes and commnets ... use the API's of post routes


export default router;
