import type { Response } from 'express';
import { StatusCodes } from 'http-status-codes';
import mongoose from 'mongoose';
import Habit from '../models/Habit';
import User from '../models/User';
import { AuthenticatedRequest } from '../middlewares/auth';
import { updateUserHabitCompletionPercentage } from '../controllers/helper/habits';
import { getTodayDeviceDate, formatLocalDate, isSameLocalDay } from '../utils/dateUtils';

// Create a new habit
export const createHabit = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { title, icon, description } = req.body;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Validate required fields
    if (!title || !icon || !description) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Title, icon, and description are required'
      });
      return;
    }

    const newHabit = new Habit({
      userId,
      title,
      icon,
      description,
      todoList: []
    });

    const savedHabit = await newHabit.save();

    // Update user's collective habit completion percentage
    await updateUserHabitCompletionPercentage(userId);
    
    res.status(StatusCodes.CREATED).json({
      message: 'Habit created successfully',
      habit: savedHabit
    });
  } catch (error) {
    console.error('Error creating habit:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error creating habit',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all habits for the authenticated user
export const getAllHabits = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    const habits = await Habit.find({ userId }).sort({ createdAt: -1 });

    // Update user's collective habit completion percentage
    await updateUserHabitCompletionPercentage(userId);

    // Get updated user with completion percentage
    const user = await User.findById(userId).select('habitCompletionPercentage');

    res.status(StatusCodes.OK).json({
      message: 'Habits retrieved successfully',
      habits,
      habitCompletionPercentage: user?.habitCompletionPercentage || 0,
      count: habits.length
    });
  } catch (error) {
    console.error('Error fetching habits:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error fetching habits',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Get all habit titles for the authenticated user from database
export const getAllHabitTitles = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const userId = req.user?.userId;
    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    // Retrieve all habits for the user from database
    const habits = await Habit.find({ userId }).select('title _id').sort({ createdAt: -1 });

    // Extract titles from habits
    const habitTitles = habits.map(habit => ({
      id: habit._id,
      title: habit.title
    }));

    res.status(StatusCodes.OK).json({
      message: 'Habit titles retrieved successfully',
      habitTitles,
      count: habitTitles.length
    });
  } catch (error) {
    console.error('Error fetching habit titles:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error fetching habit titles',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Update a habit
export const updateHabit = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { habitId } = req.params;
    const userId = req.user?.userId;
    const updateData = req.body;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    if (!mongoose.Types.ObjectId.isValid(habitId)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Invalid habit ID'
      });
      return;
    }

    // Handle special case for updating habit completion status
    if (updateData.isCompleted !== undefined) {
      const habit = await Habit.findOne({ _id: habitId, userId });

      if (!habit) {
        res.status(StatusCodes.NOT_FOUND).json({
          message: 'Habit not found'
        });
        return;
      }

      // Get today's date based on device time, not UTC
      const today = getTodayDeviceDate();

      // Update today's todoList item
      const todayTodoIndex = habit.todoList.findIndex(item => {
        return isSameLocalDay(new Date(item.date), today);
      });

      if (todayTodoIndex !== -1) {
        habit.todoList[todayTodoIndex].isCompleted = updateData.isCompleted;
        console.log(`Updated completion status for today's date (${formatLocalDate(today)}) in habit: ${habit.title}`);
      } else {
        // Add today's entry if it doesn't exist
        habit.todoList.push({
          date: today,
          isCompleted: updateData.isCompleted
        });
        console.log(`Added today's date (${formatLocalDate(today)}) with completion status to habit: ${habit.title}`);
      }

      // Update user's collective habit completion percentage
      await updateUserHabitCompletionPercentage(userId);

      const updatedHabit = habit;

      res.status(StatusCodes.OK).json({
        message: 'Habit completion status updated successfully',
        habit: updatedHabit
      });
      return;
    }

    // Regular update for other fields
    const allowedUpdates = ['title', 'icon', 'description'];
    const filteredUpdateData: any = {};

    Object.keys(updateData).forEach(key => {
      if (allowedUpdates.includes(key)) {
        filteredUpdateData[key] = updateData[key];
      }
    });

    const updatedHabit = await Habit.findOneAndUpdate(
      { _id: habitId, userId },
      filteredUpdateData,
      { new: true, runValidators: true }
    );

    if (!updatedHabit) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Habit not found'
      });
      return;
    }

    // Update user's collective habit completion percentage if needed
    if (updatedHabit) {
      await updateUserHabitCompletionPercentage(userId);
    }

    res.status(StatusCodes.OK).json({
      message: 'Habit updated successfully',
      habit: updatedHabit
    });
  } catch (error) {
    console.error('Error updating habit:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error updating habit',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};

// Delete a habit
export const deleteHabit = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const { habitId } = req.params;
    const userId = req.user?.userId;

    if (!userId) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        message: 'User not authenticated'
      });
      return;
    }

    if (!mongoose.Types.ObjectId.isValid(habitId)) {
      res.status(StatusCodes.BAD_REQUEST).json({
        message: 'Invalid habit ID'
      });
      return;
    }

    // First find the habit to get its details before deletion
    const habitToDelete = await Habit.findOne({ _id: habitId, userId });

    if (!habitToDelete) {
      res.status(StatusCodes.NOT_FOUND).json({
        message: 'Habit not found'
      });
      return;
    }

    // Delete the habit
    const deletedHabit = await Habit.findOneAndDelete({ _id: habitId, userId });

    // Update user's collective habit completion percentage after deletion
    await updateUserHabitCompletionPercentage(userId);

    res.status(StatusCodes.OK).json({
      message: 'Habit deleted successfully',
      habit: deletedHabit
    });
  } catch (error) {
    console.error('Error deleting habit:', error);
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      message: 'Error deleting habit',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
};
